D:\github\Screen Recording\src-tauri\target\debug\deps\screen_recording_lib-5acdff5151711116.d: src\lib.rs src\../icons/icon.png D:\github\Screen\ Recording\src-tauri\target\debug\build\screen-recording-995c3a151ab02581\out/bd964268f6104387acd25aed0f85c8abb95300d16e1d972e57e65e5b97e5a89b

D:\github\Screen Recording\src-tauri\target\debug\deps\libscreen_recording_lib-5acdff5151711116.rmeta: src\lib.rs src\../icons/icon.png D:\github\Screen\ Recording\src-tauri\target\debug\build\screen-recording-995c3a151ab02581\out/bd964268f6104387acd25aed0f85c8abb95300d16e1d972e57e65e5b97e5a89b

src\lib.rs:
src\../icons/icon.png:
D:\github\Screen\ Recording\src-tauri\target\debug\build\screen-recording-995c3a151ab02581\out/bd964268f6104387acd25aed0f85c8abb95300d16e1d972e57e65e5b97e5a89b:

# env-dep:CARGO_PKG_AUTHORS=you
# env-dep:CARGO_PKG_DESCRIPTION=A Tauri App
# env-dep:CARGO_PKG_NAME=screen-recording
# env-dep:OUT_DIR=D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\screen-recording-995c3a151ab02581\\out
