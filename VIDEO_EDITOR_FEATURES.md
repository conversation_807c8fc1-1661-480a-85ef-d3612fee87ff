# 视频编辑功能说明

## 功能概述

当用户在录制设置中关闭"自动下载录制文件"选项时，录制完成后将提供强大的视频编辑功能，包括：

### 1. 视频预览功能
- **实时预览播放器**：支持播放、暂停、快进、快退等基本控制
- **双模式预览**：
  - 原始视频模式：显示完整视频，可以看到裁剪框
  - 裁剪预览模式：显示裁剪后的效果
- **时间显示**：显示当前播放位置和总时长
- **进度条**：可视化显示播放进度，支持点击跳转

### 2. 时间裁剪功能
- **精确时间选择**：使用滑块设置开始时间和结束时间
- **实时时长显示**：显示裁剪后的视频时长
- **播放范围限制**：播放时自动限制在裁剪范围内
- **快捷键支持**：
  - 空格键：播放/暂停
  - 左右箭头：快进/快退5秒
  - Ctrl+S：保存视频

### 3. 空间裁剪功能
- **可视化裁剪框**：在视频预览上显示可拖拽的裁剪框
- **多种调整方式**：
  - 拖拽裁剪框移动位置
  - 拖拽边框和角落调整大小
  - 数值输入精确设置坐标和尺寸
- **预设选项**：
  - 重置为全屏
  - 居中正方形裁剪
  - 16:9比例裁剪
  - 4:3比例裁剪
- **实时尺寸显示**：显示当前裁剪区域的像素尺寸
- **边界限制**：确保裁剪框不会超出视频边界

### 4. 高级功能
- **撤销/重做**：支持撤销和重做裁剪操作
- **重置功能**：一键恢复到原始视频状态
- **缩放预览**：支持视频预览的缩放功能
- **帧精确预览**：支持逐帧预览功能

### 5. 保存功能
- **智能处理**：根据用户设置自动选择处理方式
  - 仅时间裁剪
  - 仅空间裁剪
  - 时间+空间组合裁剪
- **进度显示**：显示处理进度和状态信息
- **格式支持**：输出WebM格式（与原录制格式一致）
- **自动命名**：生成带时间戳的文件名
- **质量保持**：保持原始视频质量不变

## 使用方法

### 基本操作流程

1. **开始录制**：按照正常流程进行屏幕录制
2. **关闭自动下载**：确保在设置中关闭了"自动下载录制文件"选项
3. **完成录制**：停止录制后，会显示录制完成界面
4. **选择编辑**：点击"编辑视频"按钮进入编辑模式
5. **调整设置**：
   - 使用时间滑块设置裁剪范围
   - 在原始视频模式下拖拽裁剪框调整空间裁剪
   - 切换到裁剪预览模式查看效果
6. **保存视频**：点击"保存视频"按钮开始处理
7. **完成**：处理完成后视频将保存到指定目录

### 键盘快捷键

- **空格键**：播放/暂停视频
- **左右箭头键**：快进/快退5秒
- **上下箭头键**：上一帧/下一帧
- **Ctrl+S**：保存视频
- **Ctrl+Z**：撤销操作
- **Ctrl+Y**：重做操作
- **Esc**：取消编辑
- **R**：重置裁剪

### 操作技巧

1. **精确裁剪**：
   - 使用数值输入框可以精确设置像素级别的裁剪
   - 时间滑块支持0.1秒精度
   - 使用键盘箭头键进行微调

2. **预览效果**：
   - 在原始模式下可以看到裁剪框的位置
   - 切换到裁剪预览模式查看最终效果
   - 播放时可以实时预览裁剪后的动态效果
   - 使用缩放功能查看细节

3. **性能优化**：
   - 较大的视频文件处理时间会更长
   - 建议在处理过程中不要进行其他操作
   - 关闭不必要的应用以释放内存

4. **批量处理**：
   - 可以多次调整设置后统一处理
   - 建议先预览效果再保存

## 技术特性

### 浏览器兼容性
- 支持现代浏览器的Canvas和MediaRecorder API
- 自动检测浏览器支持情况
- 支持WebGL加速（如果可用）

### 处理能力
- 支持任意时间范围裁剪
- 支持任意矩形区域裁剪
- 保持原始视频质量
- 30 FPS输出帧率
- 支持最大4K分辨率视频

### 文件格式
- 输入：WebM格式（录制原始格式）
- 输出：WebM格式（VP9编码）
- 保持与原录制设置一致的质量
- 支持透明度（如果原始视频包含）

### 性能指标
- 处理速度：约为视频时长的1-3倍（取决于设备性能）
- 内存使用：约为视频文件大小的2-3倍
- CPU使用：支持多线程处理

## 注意事项

1. **处理时间**：视频处理需要时间，具体取决于：
   - 视频长度
   - 裁剪复杂度
   - 设备性能
   - 同时运行的其他应用

2. **内存使用**：处理大视频文件时会占用较多内存
   - 建议至少4GB可用内存
   - 处理4K视频建议8GB以上内存

3. **文件大小**：裁剪后的文件大小取决于：
   - 裁剪后的时长
   - 裁剪后的分辨率
   - 原始视频的码率

4. **保存位置**：
   - 优先保存到用户设置的目录
   - 如果权限不足，会回退到默认视频目录
   - 确保有足够的磁盘空间

5. **浏览器限制**：
   - 单个视频文件最大2GB（浏览器限制）
   - 处理时间超过30分钟可能会超时

## 故障排除

### 常见问题

1. **视频无法加载**：
   - 检查录制是否成功完成
   - 确认浏览器支持WebM格式
   - 尝试刷新页面重新加载

2. **处理失败**：
   - 检查可用内存是否充足
   - 尝试减少裁剪范围
   - 重新录制较短的视频
   - 检查浏览器控制台错误信息

3. **保存失败**：
   - 检查磁盘空间是否充足
   - 确认保存目录的写入权限
   - 尝试更换保存目录
   - 检查文件名是否包含特殊字符

4. **预览卡顿**：
   - 降低预览质量设置
   - 关闭其他占用CPU的应用
   - 检查浏览器硬件加速设置

### 性能建议

1. **录制设置**：
   - 适当降低录制质量可以提高处理速度
   - 避免录制过长的视频
   - 使用合适的分辨率和帧率

2. **裁剪设置**：
   - 尽量减少不必要的空间裁剪
   - 优先使用时间裁剪来减少文件大小
   - 避免过于复杂的裁剪区域

3. **系统资源**：
   - 关闭其他占用内存的应用
   - 确保有足够的磁盘空间
   - 使用SSD硬盘提高处理速度

4. **浏览器优化**：
   - 使用最新版本的浏览器
   - 启用硬件加速
   - 清理浏览器缓存

## 更新日志

### v1.1.0
- 新增内置视频编辑器
- 支持时间裁剪功能
- 支持空间裁剪功能
- 添加实时预览功能
- 优化用户界面

### v1.1.1
- 增加键盘快捷键支持
- 添加撤销/重做功能
- 优化处理性能
- 修复已知bug

### 即将推出
- 音频编辑功能
- 滤镜效果
- 文字/水印添加
- 多段视频合并